
package com.ailk.newchnl.service.schedule;
import com.ailk.newchnl.constant.ChannelConstants;
import com.ailk.newchnl.dao.SyncDateForKXJJDao;
import com.ailk.newchnl.entity.ChannelSysBaseType;
import com.ailk.newchnl.entity.schedule.SyncDateForKXJJ;
import com.ailk.newchnl.util.ChannelSysBaseTypeUtil;
import com.ailk.newchnl.util.DateUtil;
import com.ailk.newchnl.util.SecureFileTransferProtocol;
import com.ailk.newchnl.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPFileFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("SyncDateForKXJJService")
public class SyncDateForKXJJServiceImpl implements SyncDateForKXJJService {
    private static final Logger logger = LoggerFactory.getLogger(SyncDateForKXJJServiceImpl.class);

    //服务端ip地址---经分
    private static String ftpIp = "";
    //用户名-----经分
    private static String ftpUserName = "";
    //密码-----经分
    private static String ftpPassword = "";
    //经分存放文件路径----经分
    private static String remotePath = "";
    //渠道存放文件的路径
    private static String url_Loca="";
    //渠道ip
    private static String localIp="";
    //渠道用户名
    private static String localUserName="";
    //渠道密码
    private static String localPassword="";
    @Resource
    private SyncDateForKXJJDao syncDateForKXJJDao;
    @Resource
    private SyncDateForKXJJService syncDateForKXJJService;

    @Override
    public void execute() throws Exception {
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(99996, null, null, null);
        try {
            if (channelSysBaseTypeList.size()>0){
                logger.info("-------开始执行配置月全渠道开新降旧用户清单数据-------");
                for (ChannelSysBaseType entity: channelSysBaseTypeList) {
                    String lastMonth = entity.getCodeName();
                    executeAll(lastMonth);
                }
            }else{
                logger.info("------  开始执行自然月全渠道开新降旧用户清单数据---------");
                //获取上个月日期
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMM");
                //获取上个月的这一天
                Date lastMonthDate = DateUtil.getNextMonth(new Date(),-2);
                String lastMonth = sdf2.format(lastMonthDate);
                executeAll(lastMonth);
            }
        }catch (Exception e){
            logger.error("全渠道开新降旧用户清单数据，失败原因是：" + e);
        }
        logger.info("-------全渠道开新降旧用户清单数据执行成功-------");
    }

    @Override
    public void executeAll(String lastMonth) throws Exception{

        logger.info("获取经分同步的推送全渠道开新降旧用户清单数据相关文件程序的配置信息===================");
        getInitInfo();
        logger.info("开始获取经分主机上的相应文件===================");
        Map condition=new HashMap();
        condition.put("billMonth",lastMonth);
        Long counts= syncDateForKXJJDao.queryInfo(condition);
        if(counts>0){
            logger.info("=====本月全渠道开新降旧用户清单数据文件已入库！数据月为："+lastMonth);
            return;
        }
        //获得远程ftp连接
        FTPClient ftpClient = SecureFileTransferProtocol.connect(ftpIp, 21, ftpUserName, ftpPassword);

        //文件前缀名
        String fileNameSuffix = "ST_MKT_KNEW_JOLD_DTL_";
        //筛选经分主机上自己需要的文件
        logger.info("开始筛选经分主机上需要的文件========================");
        FTPFile[] ftpFiles = this.selectFile(ftpClient,lastMonth, remotePath, fileNameSuffix);
       // FTPFile[] ftpFiles=new FTPFile[1];
        if (ftpFiles.length == 0) {
            logger.info("没有在对应主机上找到全渠道开新降旧用户清单数据相关文件！！！===================");
            return;
        }
        logger.info("已经找到全渠道开新降旧用户清单数据文件，开始保存到渠道主机！！！===================");
        //把筛选后的文件保存到本地
        downloadFileToLocal(ftpFiles);
        logger.info("保存全渠道开新降旧用户清单数据文件到渠道主机成功===================");
        for (FTPFile file : ftpFiles) {
            String fileName = file.getName();
            String pathName = url_Loca + fileName;
            logger.info("本次处理的文件为：" + pathName);
            FileInputStream fileInputStream = new FileInputStream(new File(pathName));
            //本地测试
            //FileInputStream fileInputStream = new FileInputStream(new File("C:/Users/<USER>/Desktop/ST_MKT_KNEW_JOLD_DTL_202505.txt"));
            Scanner scanner = new Scanner(fileInputStream, "UTF-8");
            try {
                List<String> list = new ArrayList<String>();
                String str = null;
                while (scanner.hasNextLine()) {
                    str = scanner.nextLine();
                    list.add(str);
                    //每1000行处理一次
                    if (list.size() == 1000) {
                        dealInfo(list, lastMonth);
                        list.clear();
                    }
                }
                if (!CollectionUtils.isEmpty(list)) {
                    dealInfo(list, lastMonth);
                    list.clear();
                }
                //删除远程文件
                SecureFileTransferProtocol.deleteFile(ftpIp, ftpUserName, ftpPassword, remotePath+fileName);
                logger.info("删除经分侧文件成功");
            } catch (Exception e) {
                logger.info(fileName + "=====全渠道开新降旧用户清单数据客户文件入库，数据失败", e);
            } finally {
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close();
                    } catch (IOException e) {
                        logger.error("IO异常", e);
                    }
                }
                if (scanner != null) {
                    scanner.close();
                }
            }
        }
        ftpClient.disconnect();
    }

    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void toBase(List<SyncDateForKXJJ> syncDateForKXJJList) throws Exception {
        for (SyncDateForKXJJ entity:syncDateForKXJJList){
            try {
                if (entity != null) {
                    syncDateForKXJJDao.insert(entity);
                }
            }catch (Exception e){
                logger.error("入库异常",e);
            }

        }
        logger.info("全渠道开新降旧用户清单数据文件入库成功===================");
    }

    //筛选需要的文件
    public static FTPFile[] selectFile(FTPClient ftpClient, String date, String path, String fileNameSuffix) throws Exception {
        logger.info("筛选文件路径path：" + path);
        final String fileStart = fileNameSuffix + date;
        logger.info("筛选文件名称fileStart：" + fileStart);
        FTPFile[] listFiles2 = ftpClient.listFiles(path, new FTPFileFilter() {

            @Override
            public boolean accept(FTPFile arg0) {
                String name = arg0.getName();
                if (name.startsWith(fileStart)) {
                    logger.info("筛选文件名称：" + name);
                    return true;
                }
                return false;
            }
        });
        return listFiles2;
    }

    //获得配置信息
    public static void getInitInfo() throws Exception {
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 119, null, null);
        //服务端ip地址 --全渠道开新旧用降户清单数据相关文件提取
        ftpIp = channelSysBaseTypeList.get(0).getCodeName();
        //用户名            --全渠道开新降旧用户清单数据相关文件提取
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 120, null, null);
        ftpUserName = channelSysBaseTypeList.get(0).getCodeName();
        //密码                   --全渠道开新降旧用户清单数据相关文件提取
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 121, null, null);
        ftpPassword = channelSysBaseTypeList.get(0).getCodeName();
        //存放文件路径   --全渠道开新降旧用户清单数据相关文件提取    访文件的路径(经分放文件的地址) --经分
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 300, null, null);
        remotePath = channelSysBaseTypeList.get(0).getCodeName();
        //获取本地的文件连接 存放文件路径 (渠道从经分取数据后保存的路径)  --渠道
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 314, null, null);
        url_Loca = channelSysBaseTypeList.get(0).getCodeName();
        //本地ip ，用户名，密码
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 63, null, null);
        localIp = channelSysBaseTypeList.get(0).getCodeName();

        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 64, null, null);
        localUserName = channelSysBaseTypeList.get(0).getCodeName();

        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 65, null, null);
        localPassword = channelSysBaseTypeList.get(0).getCodeName();
    }

    //处理文件内容
    public void dealInfo(List<String> list, String lastMonth) throws Exception {
        List<SyncDateForKXJJ> syncDateForKXJJList = new ArrayList<SyncDateForKXJJ>();

        for (int i = 0; i < list.size(); i++) {
            SyncDateForKXJJ syncDate = new SyncDateForKXJJ();
            try {
                String[] parts = list.get(i).split("\\,", -1);

                // 处理所有新字段
                if (parts.length > 0 && StringUtils.isNotNullOrBlank(parts[0])) {
                    syncDate.setUserId(parts[0].trim());          // USER_ID
                }
                if (parts.length > 1 && StringUtils.isNotNullOrBlank(parts[1])) {
                    syncDate.setPhoneNo(parts[1].trim());        // PHONE_NO
                }
                if (parts.length > 2 && StringUtils.isNotNullOrBlank(parts[2])) {
                    syncDate.setOfferId(parts[2].trim());        // OFFER_ID
                }
                if (parts.length > 3 && StringUtils.isNotNullOrBlank(parts[3])) {
                    syncDate.setOfferName(parts[3].trim());      // OFFER_NAME
                }
                if (parts.length > 4 && StringUtils.isNotNullOrBlank(parts[4])) {
                    syncDate.setOfferFeeNext(parts[4].trim());   // OFFER_FEE_NEXT
                }
                if (parts.length > 5 && StringUtils.isNotNullOrBlank(parts[5])) {
                    syncDate.setJoinDate(parts[5].trim());       // JOIN_DATE
                }
                if (parts.length > 6 && StringUtils.isNotNullOrBlank(parts[6])) {
                    syncDate.setOpId(parts[6].trim());          // OP_ID
                }
                if (parts.length > 7 && StringUtils.isNotNullOrBlank(parts[7])) {
                    syncDate.setOrgId(parts[7].trim());          // ORG_ID
                }
                if (parts.length > 8 && StringUtils.isNotNullOrBlank(parts[8])) {
                    syncDate.setOpName(parts[8].trim());         // OP_NAME
                }
                if (parts.length > 9 && StringUtils.isNotNullOrBlank(parts[9])) {
                    syncDate.setOrgName(parts[9].trim());        // ORG_NAME
                }
                if (parts.length > 10 && StringUtils.isNotNullOrBlank(parts[10])) {
                    syncDate.setAreaId(parts[10].trim());        // AREA_ID
                }
                if (parts.length > 11 && StringUtils.isNotNullOrBlank(parts[11])) {
                    syncDate.setAreaName(parts[11].trim());     // AREA_NAME
                }
                if (parts.length > 12 && StringUtils.isNotNullOrBlank(parts[12])) {
                    syncDate.setJdUserId(parts[12].trim());     // JD_USER_ID
                }
                if (parts.length > 13 && StringUtils.isNotNullOrBlank(parts[13])) {
                    syncDate.setJdPhoneNo(parts[13].trim());     // JD_PHONE_NO
                }
                if (parts.length > 14 && StringUtils.isNotNullOrBlank(parts[14])) {
                    syncDate.setOfferFee1(parts[14].trim());     // OFFER_FEE_1
                }
                if (parts.length > 15 && StringUtils.isNotNullOrBlank(parts[15])) {
                    syncDate.setOfferFee1Next(parts[15].trim()); // OFFER_FEE_1_NEXT
                }
                if (parts.length > 16 && StringUtils.isNotNullOrBlank(parts[16])) {
                    syncDate.setJdCreateDate(parts[16].trim());  // JD_CREATE_DATE
                }
                if (parts.length > 17 && StringUtils.isNotNullOrBlank(parts[17])) {
                    syncDate.setJdOpId(parts[17].trim());        // JD_OP_ID
                }
                if (parts.length > 18 && StringUtils.isNotNullOrBlank(parts[18])) {
                    syncDate.setJdOrgId(parts[18].trim());      // JD_ORG_ID
                }
                if (parts.length > 19 && StringUtils.isNotNullOrBlank(parts[19])) {
                    syncDate.setJdOpName(parts[19].trim());      // JD_OP_NAME
                }
                if (parts.length > 20 && StringUtils.isNotNullOrBlank(parts[20])) {
                    syncDate.setJdOrgName(parts[20].trim());    // JD_ORG_NAME
                }
                if (parts.length > 21 && StringUtils.isNotNullOrBlank(parts[21])) {
                    syncDate.setJdAreaId(parts[21].trim());      // JD_AREA_ID
                }
                if (parts.length > 22 && StringUtils.isNotNullOrBlank(parts[22])) {
                    syncDate.setJdAreaName(parts[22].trim());    // JD_AREA_NAME
                }

            } catch (Exception e) {
                logger.error("第" + (i + 1) + "行的数据异常，文件解析出错！！！" + e);
                continue;
            }

            // 设置数据月份
            syncDate.setBillMonth(lastMonth);
            syncDateForKXJJList.add(syncDate);
        }

        syncDateForKXJJService.toBase(syncDateForKXJJList);
        syncDateForKXJJList.clear();
    }

    //下载文件到本地
    private void downloadFileToLocal(FTPFile[] ftpFiles) throws Exception {
        //把筛选后的文件保存到本地
        String ftpPath = "";
        String homePath = "";
        String fileName = null;
        for (FTPFile file : ftpFiles) {
            fileName = file.getName();
            ftpPath = remotePath + fileName;
            homePath = url_Loca + fileName;
            SecureFileTransferProtocol.download(ftpIp, ftpUserName, ftpPassword, ftpPath, homePath);
            logger.info("文件保存在渠道侧主机成功");
        }
        logger.info("ftpIp:" + ftpIp);
        logger.info("ftpUserName:" + ftpUserName);
        logger.info("ftpPassword:" + ftpPassword);
        logger.info("ftpPath:" + ftpPath);
        logger.info("homePath:" + homePath);
    }
}