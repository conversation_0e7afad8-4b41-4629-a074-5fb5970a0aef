<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.slf4j</groupId>
	<artifactId>slf4j-parent</artifactId>
	<version>1.6.1</version>

	<packaging>pom</packaging>
	<name>SLF4J</name>

	<url>http://www.slf4j.org</url>

	<organization>
		<name>QOS.ch</name>
		<url>http://www.qos.ch</url>
	</organization>
	<inceptionYear>2005</inceptionYear>

  <licenses>
    <license>
      <name>MIT License</name>
      <url>http://www.opensource.org/licenses/mit-license.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <properties>
    <slf4j.api.minimum.compatible.version>1.6.0</slf4j.api.minimum.compatible.version>
    <cal10n.version>0.7.4</cal10n.version>
    <log4j.version>1.2.16</log4j.version>
  </properties>


	<modules>
		<module>slf4j-api</module>
    <module>slf4j-simple</module>  
    <module>slf4j-nop</module>
		<module>slf4j-jdk14</module>
    <module>slf4j-log4j12</module>
    <module>slf4j-jcl</module>
    <module>slf4j-ext</module>  
    <module>jcl-over-slf4j</module>
    <module>log4j-over-slf4j</module>
    <module>jul-to-slf4j</module>
    <module>integration</module>
    <module>slf4j-site</module>
    <module>slf4j-migrator</module>
  </modules>

	<dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
		</dependency>
	</dependencies>

 
 <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>log4j</groupId>
        <artifactId>log4j</artifactId>
        <version>${log4j.version}</version>
      </dependency>

    <dependency>
      <groupId>ch.qos.cal10n</groupId>
      <artifactId>cal10n-api</artifactId>
      <version>${cal10n.version}</version>
    </dependency>		

   </dependencies>
 </dependencyManagement>


	<build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.3</source>
					<target>1.3</target>
				</configuration>
			</plugin>
 
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<forkMode>once</forkMode>
					<reportFormat>plain</reportFormat>
					<trimStackTrace>false</trimStackTrace>
					<excludes>
						<exclude>**/AllTest.java</exclude>
						<exclude>**/PackageTest.java</exclude>
					</excludes>
				</configuration>
			</plugin>		

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>        
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>              
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
        	<aggregate>true</aggregate>
          <excludePackageNames>org.slf4j.migrator:org.slf4j.migrator.*</excludePackageNames>
					<links>
						<link>
							http://java.sun.com/j2se/1.5.0/docs/api
						</link>
					</links>
          <groups>
            <group>
              <title>SLF4J packages</title>
              <packages>org.slf4j:org.slf4j.*</packages>
            </group>

            <group>
              <title>SLF4J extensions</title>
              <packages>org.slf4j.profiler:org.slf4j.ext:org.slf4j.instrumentation:org.slf4j.agent</packages>
            </group>

            <group>
              <title>Jakarta Commons Logging packages</title>
              <packages>org.apache.commons.*</packages>
            </group>

            <group>
              <title>Apache log4j</title>
              <packages>org.apache.log4j</packages>
            </group>

            <group>
              <title>java.util.logging (JUL) to SLF4J bridge</title>
              <packages>org.slf4j.bridge</packages>
            </group>

          </groups>
        </configuration>
      </plugin>


      <!-- as suggested in http://bugzilla.slf4j.org/show_bug.cgi?id=152 -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>parse-version</id>
            <goals>
              <goal>parse-version</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>

	</build>

  <profiles>
    <profile>
      <id>skipTests</id>     
      <properties>
        <maven.test.skip>true</maven.test.skip>
      </properties>
    </profile>
     <profile>
      <id>osgi</id>
      <modules>
         <module>osgi-over-slf4j</module>
         <module>slf4j-osgi-test-bundle</module>
         <module>slf4j-osgi-integration-test</module>
      </modules>

      <repositories>
        <repository>
          <id>m2apache.snapshots</id>
          <url>http://people.apache.org/repo/m2-snapshot-repository</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>

        <repository>
          <id>springframework.org</id>
          <name>Springframework Maven SNAPSHOT Repository</name>
          <url>http://static.springframework.org/maven2-snapshots/</url>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>

      </repositories>

      <pluginRepositories>
        <pluginRepository>
          <id>apache.snapshots</id>
          <name>Apache Snapshot Plugin Repository</name>
          <url>http://people.apache.org/repo/m2-snapshot-repository</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled> 
          </snapshots>
        </pluginRepository>
      </pluginRepositories>

    </profile>


    <profile>
      <id>javadocjar</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <pluginRepositories>
    <pluginRepository>
      <id>apache.snapshots</id>
      <name>Apache Snapshot Plugin Repository</name>
      <url>http://people.apache.org/repo/m2-snapshot-repository</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled> 
     </snapshots>
    </pluginRepository>
  </pluginRepositories>
  
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-project-info-reports-plugin</artifactId>
				<reportSets>
					<reportSet><reports/></reportSet>
				</reportSets>
			</plugin>	    
      
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <descriptors>
            <descriptor>
              src/main/assembly/source.xml
            </descriptor>
          </descriptors>
					<finalName>slf4j-${project.version}</finalName>
					<appendAssemblyId>false</appendAssemblyId>
					<outputDirectory>target/site/dist/</outputDirectory>
				</configuration>
			</plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <configuration>
          <aggregate>true</aggregate>
          <javadocDir>target/site/apidocs/</javadocDir>
          <linkJavadoc>true</linkJavadoc>
        </configuration>
      </plugin>


    </plugins>

	</reporting>
	
	  
  <scm>
		<connection>scm:svn:http://svn.slf4j.org/repos/slf4j/trunk</connection>
		<developerConnection>scm:svn:https://svn.slf4j.org/repos/slf4j/trunk</developerConnection>
		<url>http://svn.slf4j.org/viewvc/slf4j/trunk/</url>
	</scm>
	
	<distributionManagement>
		<site>
			<id>pixie</id>
			<url>scp://pixie.qos.ch/var/www/www.slf4j.org/htdocs/</url>
		</site>

    <repository>
      <id>pixie</id>
      <url>scp://pixie.qos.ch/var/mvnrepo/</url>
    </repository>

	</distributionManagement>
  
</project>