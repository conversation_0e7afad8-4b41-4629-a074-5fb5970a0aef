<%--
 * 
 * $Id: pointsAccountCancel.jsp,v 1.3 2014/10/16 03:40:14 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>装修积分,积分账户销户</title>
<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body style="padding: 10px;">
	<!-- here we will place the layout --->
	<div id="layoutObj"></div>
	<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
	<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
	<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
	<script type="text/javascript" src="${ctx}/js/module/channelPoints/pointsItem.js"></script>
	<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
	<script>
		$("#layoutObj").css("height", document.documentElement.clientHeight);
		BUI.use([ 'module/channelPoints/channelPointsAccountCancel' ], function(ChannelPointsAccountCancel) {
			new ChannelPointsAccountCancel();
		});
	</script>
</body>
</html>