<!--
 * 
 * $Id: invoceprint.jsp,v 1.2 2015/06/03 07:25:47 fuqiang Exp $
 * Copyright 2015 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<html>
<head>
<title>发票领用打印</title>
</head>
<style type="text/css">
<!--
.unnamed1 {
	border: 0px 0;
}
-->
</style>
<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
	<div align="center" >
	&nbsp;&nbsp;
		<p>
			<b><font size="5">&nbsp;&nbsp;&nbsp; 上海渠道管理系统发票领用单</font></b>
		</p>
		 <table border="1" width="580" id="table1" cellspacing="0"
			cellpadding="0" bordercolorlight="#000000" bordercolordark="#000000">
			<tr>
				<td width="102" align="center">代理商名称:</td>
				<td align="center" width="420" colspan="3"><input
					name='agentName' type="text" readonly="readonly" class="unnamed1" value="${strAgentName} "
					size="60"></td>
			</tr>
			<tr>
				<td width="102" align="center">发票类型:</td>
				<td align="center" width="420" colspan="3"><input
					name='invoiceType' type="text" readonly="readonly" class="unnamed1" value="${strBookId} "
					size="30"></td>
			</tr>
			<tr>
				<td width="102" align="center">起始号码:</td>
				<td align="center" width="124"><input name='startId' value="${strStartId} "
					type="text" class="unnamed1" readonly="readonly" size="12"></td>
				<td align="center" width="76">终止号码:</td>
				<td align="center" width="149"><input name='endId' type="text" value="${strEndId} "
					class="unnamed1" readonly="readonly" size="12"></td>
			</tr>
			<tr>
				<td width="102" align="center">数量:</td>
				<td align="center" width="420" colspan="3"><input name='count' value="${lAmount} "
					type="text" readonly="readonly" class="unnamed1" size="40">
				</td>
			</tr>
			<tr>
				<td width="102" align="center">领用日期:</td>
				<td align="center" width="420" colspan="3"><input name='date' value="${strDoneDate} "
					type="text" readonly="readonly" class="unnamed1" class="date"
					size="40"></td>
			</tr>
		</table> 
		<p>开单人:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			接收人:</p>
	</div>
</body>
<script type="text/javascript" defer="defer">
	window.print();
</script>
</html>