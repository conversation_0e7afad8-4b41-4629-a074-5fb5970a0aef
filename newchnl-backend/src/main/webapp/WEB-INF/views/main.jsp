<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/header.jsp"%>
<%@ page import="com.ailk.newchnl.util.ChannelUtil" %>
<%@ page import="com.ailk.newchnl.entity.SPrivData" %>
<html>
<head>
	<title>渠道管理系统</title>
	<!-- <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css"> -->	
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/skins/skyblue/dhtmlx.css">
	<link href="css/main.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	
		<style  type="text/css">
			html,body{
				width: 100%;
				height: 100%;
				margin: 0px;
				padding: 0px;
				overflow: hidden;
			}
		</style>
	
</head>
<body>
	<div id="header" class="header">
		<div class="dl-title">
			<!--<span>上海移动渠道管理系统</span>-->
			<img src="img/logo.png"/>
		</div>
		<%-- <shiro:guest></shiro:guest><shiro:user></shiro:user><shiro:authenticated></shiro:authenticated>--%>
		<div class="dl-log">
			欢迎您，<span class="dl-log-user"><%-- <shiro:principal/> --%><%=ChannelUtil.getSessionSPrivDataInfo(request).getOpName() %>（<%=ChannelUtil.getSessionSPrivDataInfo(request).getOrgName() %>）</span>
			<a href="#" title="退出系统" id='logout' class="dl-log-quit">[退出]</a>
			<%--<a href="#" title="修改密码" id='changePwd' class="dl-log-quit">[修改密码]</a>--%>
		</div>
	</div>
	
	<script type="text/javascript" src="assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
	<script type="text/javascript" src="assets/bui/seed-min.js" data-debug="true"></script>
	<script type="text/javascript" src="js/common/config.js"></script>
	<script type="text/javascript" src="${ctx}/js/common/waterMark.js"></script>
    <script type="text/javascript" src="${ctx}/js/common/pointer_events_polyfill.js"></script>
    <script>
		var channelSysBaseType = <%=ChannelUtil._CHANNEL_SYS_BASE_TYPE%> ;
        var chnlResModelDefinition = <%=ChannelUtil._CHNL_RES_MODEL_DEFINITION%> ;
        var chnlResType = <%=ChannelUtil._CHNL_RES_TYPE%> ;

		var columnFormatter = function (codeType,codeId,tag,externCode,ext1,ext2){
			if(channelSysBaseType[codeType]){
				var list = channelSysBaseType[codeType];
				for(var i=0;i<list.length;i++){
                    if(codeId){
                        if(list[i].codeId != codeId){
                            continue;
                        }
                    }
                    if(tag){
                        if(list[i].tag != tag){
                            continue;
                        }
                    }
                    if(externCode){
                        if(list[i].externCode != externCode){
                            continue;
                        }
                    }
                    if(ext1){
                        if(list[i].ext1 != ext1){
                            continue;
                        }
                    }
                    if(ext2){
                        if(list[i].ext2 != ext2){
                            continue;
                        }
                    }
                    return list[i].codeName;
				}
			}
			return codeId;
		};

        /**
         * 资源类型定义枚举
         */
        var chnlResTypeFormatter = function (resTypeId){
            for(var i=0;i<chnlResType.length;i++){
                if(resTypeId){
                    if(chnlResType[i].resTypeId != resTypeId){
                        continue;
                    }
                }
                return chnlResType[i].resName;
            }
            return resTypeId;
        };

        /**
         * 资源定义枚举
         */
        var chnlResModelDefinitionFormatter = function (resCode){
            for(var i=0;i<chnlResModelDefinition.length;i++){
                if(resCode){
                    if(chnlResModelDefinition[i].resCode != resCode){
                        continue;
                    }
                }
                return chnlResModelDefinition[i].resName;
            }
            return resCode;
        };
		
		/**
		* codeType 枚举类型
		* haveAll 展示时要不加全部 0：不需要 1：需要
		* codeId 枚举的键 key
		* tag 
		* externCode
		* ext1 渠道编码
		* ext2 
		*/
		var getCodeType = function(codeType,haveAll,codeId,tag,externCode,ext1,ext2){
			var reList= [];
			
			if(haveAll && haveAll == 1){
				var obj = {};
				obj.value = "";
				obj.text = "全部";
				reList.push(obj);
			}
			
			if(channelSysBaseType[codeType]){
				var list = channelSysBaseType[codeType];
				for(var i=0;i<list.length;i++){
					var id = list[i].codeId;
					var text = list[i].codeName;
					
					if(codeId){
						if(list[i].codeId != codeId){
							continue;
						}
					}
					
					if(tag){
						if(list[i].tag != tag){
							continue;
						}
					}
					if(externCode){
						if(list[i].externCode != externCode){
							continue;
						}
					}
					if(ext1){
						if(list[i].ext1 != ext1){
							continue;
						}
					}
					if(ext2){
						if(list[i].ext2 != ext2){
							continue;
						}
					}
					
					var obj = {};
					obj.value = ""+id;
					obj.text = text;
					reList.push(obj);
				}
			}
			return reList;
		};

        /**
         * 根据codeType 和 codeId 获取枚举
         * @param codeType
         * @param codeId
         * @returns {Array}
         */
        var getCodeTypes = function(codeType,codeId){
            var reList= [];
            if(channelSysBaseType[codeType]){
                var list = channelSysBaseType[codeType];
                for(var i=0;i<list.length;i++){
                    if(codeId){
                        if(list[i].codeId != codeId){
                            continue;
                        }
                    }
                    reList.push(list[i]);
                }
            }
            return reList;
        };

		var getComboboxData = function(codeType,haveAll,codeId,tag,externCode,ext1,ext2){
			var data_ = BUI.getCodeType(codeType,haveAll,codeId,tag,externCode,ext1,ext2);
			var options = {
				data : data_,
				textField:'text',
				valueField:'value'
			};
			return options;
		}
		
		var getComboData = function(codeType,haveAll,codeId,tag,externCode,ext1,ext2){
			return BUI.getCodeType(codeType,haveAll,codeId,tag,externCode,ext1,ext2);
		}
		
	
		var mainPage ;
	    BUI.use('common/main',function(MainPage){
			$.ajax({
				url:'${ctx}/menu?time='+new Date(),
				dataType:'json',
				success:function(response,status,xhr){
					if (response.status == 0){
						var navData = response.data;
						mainPage = new MainPage(navData);
                        //mainPage.loadNotice();
					}else{
			    		dhtmlx.alert({
			    			title:"系统错误",text:response.msg,type:"alert-error",ok:"确定"
			    		});
					}
				}
			});
			
			$("#logout").click(function(){
				if (window.confirm("确定要退出系统吗？")) {
                    var url = "${ctx}/sso/logout";
                    //url = "${ctx}/logout";
					$.post(url, function () {
						alert("感谢您的使用！");
						try {
							parent.window.opener = null;
							parent.window.open("", "_self");
							parent.window.close();
				            if (window && window.location && window.location.href) {
								window.location.href = "about:blank";
							}
						} catch (a) {}
					});
				}
			});
			
			$("#changePwd").click(function(){
				$.post("${ctx}/user/page/changePassword", function () {
					try {
						parent.window.opener = null;
						parent.window.open("", "_self");
			            if (window && window.location && window.location.href) {
			            	var pageInfo ={};
			            	pageInfo.id = "id999";
			 		        pageInfo.title = "修改密码";
			 		        pageInfo.href = BUI.ctx+"/user/page/changePassword";
			 		        pageInfo.isClose = false;
			 		        pageInfo.closeable = true;
			 		        parent.mainPage.openPage(pageInfo);
						}
					} catch (a) {}
				});
			});
		});
	</script>
</body>
</html>
