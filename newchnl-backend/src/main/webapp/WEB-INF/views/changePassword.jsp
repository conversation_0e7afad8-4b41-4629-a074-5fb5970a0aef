<%--
 * 
 * $Id: changePassword.jsp,v 1.2 2014/09/25 11:30:16 xuwz Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>修改密码</title>
<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body style="padding: 10px;">
	<!-- here we will place the layout --->
	<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
	<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
	<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
	<script>
		var _userName = "${userName}";
		BUI.use([ 'module/archive/changePassword' ], function(ChangePassword) {
			new ChangePassword();
		});
	</script>
</body>
</html>