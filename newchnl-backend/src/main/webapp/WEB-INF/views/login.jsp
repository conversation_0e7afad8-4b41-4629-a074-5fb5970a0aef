<%@page import="org.apache.shiro.authc.AuthenticationException"%>
<%@page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter"%>
<%@page contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
    <link href="assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="css/common.css">
	<link rel="stylesheet" type="text/css" href="css/login.css">
	<style  type="text/css">
		html,body{
			overflow: hidden;
		}
	</style>
	
</head>
<body class="loginpage">
	<div class="loginbox">
    	<div class="loginboxinner">
            <div class="logo">
            	<h1 class="logo">上海移动渠道管理系统</h1>
            </div>
            <form id="loginForm" method="post">
                <div class="username">
                	<div class="usernameinner">
                    	<input type="text" id="username" name="username" value="" placeholder="请输入用户名">
                    </div>
                </div>
                <div class="password">
                	<div class="passwordinner">
                    	<input type="password" id="password" name="password" value="" placeholder="请输入密码">
                    </div>
                </div>
                <div class="validatecode">
                	<div class="validatecodeinner">
                    	<input type="text" id="verificationCode" name="verificationCode" value="" placeholder="请输入验证码">
                    	<img id="verificationCodeImg" src="${ctx}/verificationCode" title="看不清，换一张">
                    </div>
                </div>
                <button id="loginBtn" class="button button-primary">登录</button>
            </form>
            <div id="result" style="color: red;"></div>
        </div>
    </div>
    
	<script type="text/javascript" src="assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="assets/bui/seed-min.js" data-debug="true"></script>
	<script type="text/javascript" src="js/common/config.js"></script>
	<script>
		BUI.use('common/login',function(Login){
			new Login();
		});
	</script>
	<%
		Object obj = request.getAttribute(FormAuthenticationFilter.DEFAULT_ERROR_KEY_ATTRIBUTE_NAME);
		AuthenticationException authExp = (AuthenticationException)obj;
		String message = "";
		if(authExp != null){
			message = authExp.getMessage();
		}
	%>
	<script type="text/javascript">
		$("#result").html("<%=message %>");
	</script>
</body>
</html>