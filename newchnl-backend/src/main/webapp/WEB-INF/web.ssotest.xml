<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="3.0"
	xmlns="http://java.sun.com/xml/ns/javaee" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">
	<display-name>newchnl-backend</display-name>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath:spring-common.xml,
			classpath:spring-remoting-client.xml
		</param-value>
	</context-param>

    <listener>
        <listener-class>com.ailk.newchnl.web.listener.ContextLoaderListener</listener-class>
    </listener>
	
	<!-- session超时时间 -->
	<session-config>
		<session-timeout>60</session-timeout>
	</session-config>
	
	<!-- shiro 登陆控制 -->
	<!--<filter>
		<filter-name>shiroFilter</filter-name>
		<filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
		<init-param>
			<param-name>targetFilterLifecycle</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>shiroFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>-->
 	
 	<servlet>
		<servlet-name>mvc</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:spring-mvc-servlet.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
 		<servlet-name>mvc</servlet-name>
 		<url-pattern>/</url-pattern>
 	</servlet-mapping>
 	
 	<!-- sso 登陆控制 -->
 	<filter>
		<filter-name>portalfirstfilter</filter-name>
		<filter-class>com.asiainfo.portal.framework.external.PortalFirstFilter</filter-class>
		<!-- 接入系统名称，目前有CRM、BOSS、ESOP，不能自行定义 -->
		<init-param>
			<param-name>client-name</param-name>
			<param-value>CRM</param-value>
		</init-param>
		
		<!-- CRM-SSO Server认证中心域名地址-->
		<!-- 生产环境  http://sso.crm.sh.cmcc -->
		<!-- 测试环境 http://ssolt.crm.sh.cmcc:5555 -->
		<init-param>
			<param-name>portal-servername</param-name>
			<param-value>http://ssolt.crm.sh.cmcc:5555</param-value>
			<!--<param-value>http://sso.crm.sh.cmcc</param-value>-->
		</init-param>
		
		<!-- 接入系统模拟实现类全路径 -->
		<init-param>
			<param-name>impl-classname</param-name>
			<param-value>com.ailk.newchnl.portal.ChannelPopedomImpl</param-value>
		</init-param>
		
		<init-param>
			<param-name>4a-servername</param-name>
			<param-value></param-value>
		</init-param>
		
		<!--允许直接访问，无需校验session的地址 -->
		<init-param>
			<param-name>allow-path</param-name>
			<param-value>*.jpg;*.png;*.ico;*.gif;*.css;*.js;/detect.html;/dealMessage</param-value>
		</init-param>
	
		<init-param>
			<param-name>is-log</param-name>
			<param-value>true</param-value>
		</init-param>
		
		<!-- 报活方式，post:httppost模拟报文报活;redirect：页面跳转方式报活 -->
		<init-param>
			<param-name>active-method</param-name>
			<param-value>post</param-value>
		</init-param>
		
		<!-- 报活间隔时间，单位分钟 -->
		<init-param>
			<param-name>active-interval-minute-time</param-name>
			<param-value>10</param-value>
		</init-param>
		
		<!-- 顶级域名，会话cookie作用域 -->
	 	<init-param>
			<param-name>cookie-domain</param-name>
			<param-value>sh.cmcc</param-value>
		</init-param>
	</filter>
	<!-- 需要过滤的路径和文件类型 -->
	<filter-mapping>
		<filter-name>portalfirstfilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<servlet>
		<servlet-name>SSOLogout</servlet-name>
		<servlet-class>com.asiainfo.portal.framework.external.LogoutServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>SSOLogout</servlet-name>
		<url-pattern>/sso/logout</url-pattern>
	</servlet-mapping>
 	<!-- SSO 登陆控制结束 -->

    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>HiddenHttpMethodFilter</filter-name>
        <filter-class>org.springframework.web.filter.HiddenHttpMethodFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>HiddenHttpMethodFilter</filter-name>
        <servlet-name>mvc</servlet-name>
    </filter-mapping>


    <!-- 记录操作路径 -->
    <!--
    <filter>
        <filter-name>trackFilter</filter-name>
        <filter-class>com.ailk.newchnl.web.filter.TrackFilter</filter-class>
        <init-param>
            <param-name>allow-path</param-name>
            <param-value>.jpg;.png;.ico;.gif;.css;.js;.swf;/detect.html;/;.eot</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>trackFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    -->

	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
		<welcome-file>index.htm</welcome-file>
		<welcome-file>index.jsp</welcome-file>
		<welcome-file>default.html</welcome-file>
		<welcome-file>default.htm</welcome-file>
		<welcome-file>default.jsp</welcome-file>
	</welcome-file-list>

	<mime-mapping>
		<extension>eot</extension>
		<mime-type>application/octet-stream</mime-type>
	</mime-mapping>

    <error-page>
        <error-code>404</error-code>
        <location>/404.html</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/500.html</location>
    </error-page>
</web-app>
