<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="httpInvokerRequestExecutor"
          class="org.springframework.remoting.httpinvoker.HttpComponentsHttpInvokerRequestExecutor">
        <property name="connectTimeout" value="600000"/>
        <property name="readTimeout" value="1200000"/>
    </bean>

    <bean id="userService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/UserService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.UserService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 保存用戶登錄信息管理 -->
    <bean id="chnlPortalLoginInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlPortalLoginInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlPortalLoginInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 用户角色管理 -->
    <!--  <bean id="userRoleService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
         <property name="serviceUrl" value="${system.service.url}/UserRoleService" />
         <property name="serviceInterface" value="com.ailk.newchnl.service.UserRoleService" />
         <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
     </bean> -->
    <bean id="commerceCircleService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/CommerceCircleService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.CommerceCircleService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="familyAreaInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/FamilyAreaInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.FamilyAreaInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="channelSysBaseTypeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSysBaseTypeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelSysBaseTypeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="organizeTreeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/OrganizeTreeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.OrganizeTreeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 集中地配置 -->
    <bean id="channelCentrallyService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelCentrallyService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelCentrallyService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 终端支撑网点信息管理 -->
    <bean id="channelSupportOffceService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSupportOffceService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelSupportOffceService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 终端手机卖场信息管理 -->
    <bean id="channelSupportPhoneStoreService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSupportPhoneStoreService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelSupportPhoneStoreService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 网点信息 -->
    <bean id="channelNodeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 地推授权牌信息 -->
    <bean id="authorizationCardInfoService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AuthorizationCardInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AuthorizationCardInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 客户经理管理 -->
    <bean id="accountManagerService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AccountManagerService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AccountManagerService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 代理商信息 -->
    <bean id="channelAgentService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- soa -->
    <bean id="channelSoaService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSoaService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelSoaService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- realName 实名制配置 -->
    <bean id="channelNodeRealNameService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeRealNameService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeRealNameService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!--jms配置-->
    <bean id="jmsRegisterService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/JmsRegisterService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.JmsRegisterService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    
    <!--insprotocalService配置-->
    <bean id="insprotocalService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/InsprotocalService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.InsprotocalService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- entityBasiInfo配置 -->
    <bean id="channelEntityBasicInfoService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelEntityBasicInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelEntityBasicInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="workFlowService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/WorkFlowService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.WorkFlowService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>


    <!-- 配置实体批量进度查询 -->
    <bean id="entityBatchSyncFileService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/EntityBatchSyncFileService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.EntityBatchSyncFileService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 网点额度调整 -->
    <bean id="nodeLimitAdjustService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/NodeLimitAdjustService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.NodeLimitAdjustService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 网点资质调整 -->
    <bean id="nodeAptitideAjustService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/NodeAptitideAjustService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.NodeAptitideAjustService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 网点年检查询 -->
    <bean id="chnlNodeYearCheckInfoService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlNodeYearCheckInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlNodeYearCheckInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 网点星级报表查询 -->
    <bean id="chnlNodeStarCheckService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlNodeStarCheckService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlNodeStarCheckService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 科目信息配置 -->
    <bean id="agentSubjectService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentSubjectService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentSubjectService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 银行和代理商关联表信息配置 -->
    <bean id="channelEntityAccRelService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelEntityAccRelService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelEntityAccRelService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 罚金明细信息管理 -->
    <bean id="agentBusiFundDtlService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentBusiFundDtlService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentBusiFundDtlService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 罚金添加信息表 -->
    <bean id="agentFundDtlService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentFundDtlService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentFundDtlService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 报表 -->
    <bean id="channelReportConfigService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelReportConfigService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelReportConfigService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 卡号分离短信激活信息管理配置 -->
    <bean id="chnlSmsActiveNodeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlSmsActiveNodeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlSmsActiveNodeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 实名制拍照软件信息管理 -->
    <bean id="chnlRealNameSoftwareInfoService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlRealNameSoftwareInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlRealNameSoftwareInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 代理商服务号码配置 -->
    <bean id="channelAgentNumService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentNumService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentNumService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 实体对应关系管理配置 -->
    <bean id="channelEntityMatchOrgService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelEntityMatchOrgService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelEntityMatchOrgService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 虚拟工号与CRM工号绑定 -->
    <bean id="virtualMatchCrmService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/VirtualMatchCrmService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.VirtualMatchCrmService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 激励积分 -->
    <bean id="encouragePointsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/EncouragePointsService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.EncouragePointsService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 保证金负责人 -->
    <bean id="agentDepositLinkmanInfoService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentDepositLinkmanInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentDepositLinkmanInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 业务量管理 -->
    <bean id="agentBusiAmountService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentBusiAmountService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentBusiAmountService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 合作方选号级别 -->
    <bean id="agentNumLevelService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentNumLevelService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentNumLevelService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 合作方选资金账户 -->
    <bean id="channelAccountService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAccountService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAccountService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 巡检网点分配管理 -->
    <bean id="channelInspectorInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelInspectorInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelInspectorInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--代理商的营业厅的下拉框-->
    <bean id="agentBusiService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentBusiService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentBusiService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 卡号规则配置 -->
    <bean id="cardRuleConfigService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/CardRuleConfigService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.CardRuleConfigService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 窜货数据 -->
    <bean id="gchannelGoodsSellService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/GchannelGoodsSellService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.GchannelGoodsSellService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 终端特许经营考核 -->
    <bean id="manageCheckService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ManageCheckService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ManageCheckService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 酬金预算导入 -->
    <bean id="gchannelBudgetAddService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/GchannelBudgetAddService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.GchannelBudgetAddService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 审核人 -->
    <bean id="channelNodeStarAdjManagerService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeStarAdjManagerService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeStarAdjManagerService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 发送短信 -->
    <bean id="channelNotifyService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNotifyService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNotifyService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 巡检指标管理 -->
    <bean id="channelNodePatrolIndexService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodePatrolIndexService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodePatrolIndexService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 评分审批管理 -->
    <bean id="channelInspectOpLogService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelInspectOpLogService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelInspectOpLogService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- GIS汇总报表管理 -->
    <bean id="gisPatrolGridService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/GisPatrolGridService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.GisPatrolGridService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 白卡管理 -->
    <bean id="whiteCardManageService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/WhiteCardManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.WhiteCardManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 冷号管理 -->
    <bean id="inactivePhoneManageService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/InactivePhoneManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.InactivePhoneManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 社会加盟店执行考核接口 -->
    <bean id="leagueSocialNodeExeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/LeagueSocialNodeExeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.LeagueSocialNodeExeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 网格直销服务费考核与维护 -->
    <bean id="gridDirectSellingService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/GridDirectSellingService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.GridDirectSellingService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 网点设备管理 -->
    <bean id="chnlDeviceManageService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlDeviceManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlDeviceManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 有价卡管理 -->
    <bean id="busiCardManageService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusiCardManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.BusiCardManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 有号卡管理 -->
    <bean id="resInactivePhoneManageService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ResInactivePhoneManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ResInactivePhoneManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 设备终端管理 -->
    <bean id="terminalManageService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/TerminalManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.TerminalManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- combo加载数据 -->
    <bean id="comboOptionService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ComboOptionService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ComboOptionService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 核销管理 未返卡信息 -->
    <bean id="busiPreCardService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusiPreCardService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.BusiPreCardService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 订单汇总查询 -->
    <bean id="orderTotalQueryService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/OrderTotalQueryService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.OrderTotalQueryService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 订单付费查询 -->
    <bean id="orderPayStatService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/OrderPayStatService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.OrderPayStatService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 巡检模版管理配置 -->
    <bean id="channelNodePatrolTemplateService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodePatrolTemplateService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodePatrolTemplateService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 巡检全公司查询配置 -->
    <bean id="channelInpectorNodeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelInpectorNodeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelInpectorNodeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="channelNodeMonthCostService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeMonthCostService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeMonthCostService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 营销活动管理 -->
    <bean id="channelActivityBusiService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelActivityBusiService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelActivityBusiService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="agentInfoQueryService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentInfoQueryService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentInfoQueryService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="channelNoticeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNoticeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNoticeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 渠道路径 -->
    <bean id="channelTrackService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelTrackService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelTrackService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="channelZydPhoneCheckService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelZydPhoneCheckService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelZydPhoneCheckService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="chnlNodeStarSellTrackService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlNodeStarSellTrackService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlNodeStarSellTrackService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 订单 -->
    <bean id="channelOrderService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelOrderService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelOrderService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="agentQueryRecordService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentQueryRecordService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentQueryRecordService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="macBoundInfoDtlService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/MacBoundInfoDtlService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.MacBoundInfoDtlService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="channelAssessTempService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAssessTempService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAssessTempService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="assignDtlForReportService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AssignDtlForReportService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AssignDtlForReportService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="cardStatusQueryService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/CardStatusQueryService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.CardStatusQueryService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="channelAgentLimitNumsService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentLimitNumsService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentLimitNumsService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="resCodeInfoExtSrevice" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ResCodeInfoExtSrevice"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ResCodeInfoExtSrevice"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="channelSilenceTaskService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSilenceTaskService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelSilenceTaskService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="channelSilenceTaskDtlService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSilenceTaskDtlService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelSilenceTaskDtlService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="channelNodeSilenceService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeSilenceService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeSilenceService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="silenceCompanyBindInspectorService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SilenceCompanyBindInspectorService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.SilenceCompanyBindInspectorService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="slienceReportService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SlienceReportService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.SlienceReportService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 补/换白卡 -->
    <bean id="whiteCardReplacementService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/WhiteCardReplacementService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.WhiteCardReplacementService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 单店POS订单自动分配 -->
    <bean id="whiteCardAutoDistributeService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/WhiteCardAutoDistributeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.WhiteCardAutoDistributeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="resManageService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ResManageService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ResManageService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="singlePosCancelService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SinglePosCancelService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.SinglePosCancelService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="posAssginBackService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/PosAssginBackService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.PosAssginBackService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="schedulerService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.schedule.url}/SchedulerService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SchedulerService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 内部订单流程监控 -->
    <bean id="qworkflowService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/QworkflowService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.QworkflowService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 有价卡激活查询 -->
    <bean id="busiCardActiveQueryService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusiCardActiveQueryService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.BusiCardActiveQueryService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 有号卡补卡 -->
    <bean id="chnlResAssignValueService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlResAssignValueService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlResAssignValueService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- huangdy -->
    <bean id="channelNodeXmlDataService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeXmlDataService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeXmlDataService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 渠道业务订购 -->
    <bean id="businessInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusinessInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.BusinessInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="businessRecordService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusinessRecordService" />
        <property name="serviceInterface" value="com.ailk.newchnl.service.BusinessRecordService" />
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 合作方权限分配管理 -->
    <bean id="channelAgentPhoneLevelService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentPhoneLevelService"/>
        <property name="serviceInterface"
                  value="com.ailk.newchnl.service.channelPhoneLevel.ChannelAgentPhoneLevelService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 权限分配 -->
    <bean id="channelSelectPhoneLevelService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSelectPhoneLevelService"/>
        <property name="serviceInterface"
                  value="com.ailk.newchnl.service.channelPhoneLevel.ChannelSelectPhoneLevelService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 差错费管理 -->
    <bean id="agentMistakeFeeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentMistakeFeeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentMistakeFeeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 出错原因 -->
    <bean id="agentMistakeInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentMistakeInfoService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentMistakeInfoService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 酬金管理  酬金代理商 huangdy-->
    <bean id="adjustFeeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AdjustFeeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AdjustFeeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 代理商服务费 -->
    <bean id="agentServiceFeeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentServiceFeeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentServiceFeeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="penaltyCoefficientsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/PenaltyCoefficientsService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.PenaltyCoefficientsService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="chnlResTypeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlResTypeService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlResTypeService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="chnlResModelDefinitionService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlResModelDefinitionService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlResModelDefinitionService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="chnlResInvoiceService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChnlResInvoiceService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChnlResInvoiceService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="accRedContRactService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AccRedContRactService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AccRedContRactService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="rwdYtjDataSetService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/RwdYtjDataSetService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.RwdYtjDataSetService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="channelPointsNewService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelPointsNewService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.ChannelPointsNewService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="agentAdjustAndBatchDtlService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentAdjustAndBatchDtlService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentAdjustAndBatchDtlService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="jFCountPointService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/JFCountPointService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.JFCountPointService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="basicInfprmationMaintenanceService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BasicInfprmationMaintenanceService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.BasicInfprmationMaintenanceService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>


    <!-- 装修积分 -->
    <bean id="channelPointsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelPointsService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelPointsService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="violationBusinessService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ViolationBusinessService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ViolationBusinessService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>

    </bean>

    <bean id="agentAdjustUseService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentAdjustUseService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentAdjustUseService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="sellResourceSyncService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SellResourceSyncService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.SellResourceSyncService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="shopManagerInfoToRewardService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ShopManagerInfoToRewardService"></property>
        <property name="serviceInterface"
                  value="com.ailk.newchnl.service.schedule.ShopManagerInfoToRewardService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>


    <bean id="businessDataToRewardService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusinessDataToRewardService"></property>
        <property name="serviceInterface"
                  value="com.ailk.newchnl.service.schedule.BusinessDataToRewardService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="syncNodeInfoToMallService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncNodeInfoToMallService"></property>
        <property name="serviceInterface"
                  value="com.ailk.newchnl.service.schedule.SyncNodeInfoToMallService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="broadBandChargeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BroadBandChargeService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.BroadBandChargeService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="disCountDataService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/DisCountDataService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.DisCountDataService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="disCountDataTaskService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/DisCountDataTaskService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.DisCountDataTaskService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="syncGridTreeInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncGridTreeInfoService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncGridTreeInfoService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="gridTreeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/GridTreeService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.GridTreeService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="channelFeeDtlService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelFeeDtlService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.assess.ChannelFeeDtlService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!--合作方黑名单-->
    <bean id="agentBlacklistService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentBlacklistService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentBlacklistService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="ChannelContractService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelContractService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelContractService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean id="BusiFallFeeProcessService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusiFallFeeProcessService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.BusiFallFeeProcessService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean id="ErpChannelAccountMessageService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ErpChannelAccountMessageService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ErpChannelAccountMessageService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean id="ProcessPersonRoleService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ProcessPersonRoleService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ProcessPersonRoleService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean id="crm2ChannelService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/CRM2ChannelService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.crm.CRM2ChannelService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean id="commonService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/CommonService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.CommonService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean name="indicatorManagerService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/IndicatorManagerService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.IndicatorManagerService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean name="orgDetailToXYWDService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/OrgDetailToXYWDService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.OrgDetailToXYWDService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
<!--    取号信息-->
    <bean name="QueuingInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/QueuingInfoService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.QueuingInfoService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
<!--    承包商资质管理-->
    <bean name="AgentContractorManagerService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentContractorManagerService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentContractorManagerService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
<!--    同步网点性质为电子渠道的渠道id和渠道名称信息到卡券平台-->
    <bean name="SyncNodeInfoToCouponService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncNodeInfoToCouponService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncNodeInfoToCouponService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!--    渠道二期新增“社会渠道人员补贴核算”界面-->
    <bean name="channelPeopleAccountingDtlService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/channelPeopleAccountingDtlService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.channelPeopleAccountingDtlService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="SubsidyImportTaskService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SubsidyImportTaskService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SubsidyImportTaskService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
<!--每个月6号获取经分推送异网主卡相关文件，将其内容入库保存    -->
    <bean name="SyncYwzkUserService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncYwzkUserService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncYwzkUserService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!--每个月16号获取经分推送网格承包基数、微格运营综合系数相关文件，将其内容入库保存    -->
    <bean name="SyncWgcbjsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncWgcbjsService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncWgcbjsService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="SyncWgyyzhxsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncWgyyzhxsService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncWgyyzhxsService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="AgentrRtaSjmcDyjlService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/AgentrRtaSjmcDyjlService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.AgentrRtaSjmcDyjlService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!--    每个月10号前获取酬金口径的属地渠道费用数据将其入库保存-->
    <bean name="ChannelDependencySumFeeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelDependencySumFeeService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelDependencySumFeeService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!--每个月11号获取经分推送5G终端数据、DM卡槽数据、停机数据、首次5G登录数据相关文件，将其内容入库保存    -->
    <bean name="Sync5GTerminalService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/Sync5GTerminalService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.Sync5GTerminalService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="SyncDMCardSlotService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDMCardSlotService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDMCardSlotService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="SyncShutDownService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncShutDownService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncShutDownService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="Sync5GFirstOnlineService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/Sync5GFirstOnlineService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.Sync5GFirstOnlineService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <!--每个月11号获取经分推送和彩云首次活跃积分奖励清单、和彩云留存活跃积分奖励清单、停机数据、和彩云资产上传积分奖励清单、和彩云高资产积分奖励清单，将其内容入库保存    -->
    <bean name="StMktHcyAppInviteService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/StMktHcyAppInviteService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.StMktHcyAppInviteService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean name="StMktHcyLcInviteService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/StMktHcyLcInviteService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.StMktHcyLcInviteService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean name="StMktHcyScjfInviteService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/StMktHcyScjfInviteService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.StMktHcyScjfInviteService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean name="StMktHcyHighAssentService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/StMktHcyHighAssentService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.StMktHcyHighAssentService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!--    每个月11号获取经分推送和彩云会员首次活跃积分奖励清单、和彩云会员留存活跃积分奖励清单，将其内容入库保存-->
    <bean name="StMktHcyVIPAppInviteService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/StMktHcyVIPAppInviteService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.StMktHcyVIPAppInviteService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <bean name="StMktHcyVIPLcInviteService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/StMktHcyVIPLcInviteService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.StMktHcyVIPLcInviteService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <!--    每月10号凌晨3点抽取酬金星级达标数据入库-->
    <bean name="ChannelSocialStarQueryService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelSocialStarQueryService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.ChannelSocialStarQueryService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <!-- 合作方信息入库 和查询-->
    <bean name="ChannelAgentApplicationInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentApplicationInfoService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentApplicationInfoService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <bean name="ChannelAgentApplicationFileInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentApplicationFileInfoService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentApplicationFileInfoService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!-- 合作方申请信息处理 记录操作员信息入表 -->
    <bean name="ChannelAgentOperInfoService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentOperInfoService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentOperInfoService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!-- 处理营业厅时间给能运平台，同步数据给云店 -->
    <bean id="BusinessHoursJointService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/BusinessHoursJointService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.BusinessHoursJointService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--渠道月度快速结算专项额度查询-->
    <bean id="ChannelMonthQuickTotalService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelMonthQuickTotalService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelMonthQuickTotalService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--每月16号下午14点获取经分推送顺差激活条件信息文件入库-->
    <bean id="ActivityConditionsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ActivityConditionsService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.ActivityConditionsService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--渠道合作协议实施跟踪-->
    <bean id="ChannelAgentProTracksService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelAgentProTracksService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelAgentProTracksService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--渠道合作方信息股东校验-->
    <bean id="ChannelShareholderService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelShareholderService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelShareholderService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--将健康养老活跃数据入库 -->
    <bean name="ThreeHomeWideService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ThreeHomeWideService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.ThreeHomeWideService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <!-- 集团激励文件入库 -->
    <bean name="GroupInspireService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/GroupInspireService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.GroupInspireService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <!-- 升档有效清单文件入库 -->
    <bean name="ListOfValidUpshiftsService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ListOfValidUpshiftsService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.ListOfValidUpshiftsService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>

    <!-- 每月5号 20号同步全网统一编码数据相关文件   -->
    <bean name="SyncUnifyCodeService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncUnifyCodeService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncUnifyCodeService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"></property>
    </bean>
    <!--直随销渠道黑名单-->
    <bean id="directBlacklistService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/DirectBlacklistService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.DirectBlacklistService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--    家庭云电脑活跃数据-->
    <bean id="SyncDateForJTYDNService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForJTYDNService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForJTYDNService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!--    家庭云电脑活跃数据-->
    <bean id="SyncDateForJTAFService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForJTAFService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForJTAFService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!--    承诺消费送目标客户清单数据-->
    <bean id="SyncDateForCNXFService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForCNXFService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForCNXFService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <bean id="SyncDateCHNAndBroadBandService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateCHNAndBroadBandService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateCHNAndBroadBandService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="channelNodeScoreService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/ChannelNodeScoreService"/>
        <property name="serviceInterface" value="com.ailk.newchnl.service.ChannelNodeScoreService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <bean id="SyncHotKeepDownListService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncHotKeepDownListService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncHotKeepDownListService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--    全渠道套封卡清单数据-->
    <bean id="SyncDateForTFKService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForTFKService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForTFKService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!--    全渠道开新旧用降户清单数据-->
    <bean id="SyncDateForKXJJService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForKXJJService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForKXJJService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
    <!-- 降档办业务清单数据 -->
    <bean id="SyncDateForDTLService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForDTLService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForDTLService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 云电脑终端用户稽核信息清单数据 -->
    <bean id="SyncDateForJHService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForJHService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForJHService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 清单固定费信息清单数据 -->
    <bean id="SyncDateForTSSRService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForTSSRService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForTSSRService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>

    <!-- 中高端外呼推送信息数据 -->
    <bean id="SyncDateForWHService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${system.service.url}/SyncDateForWHService"></property>
        <property name="serviceInterface" value="com.ailk.newchnl.service.schedule.SyncDateForWHService"></property>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
</beans>
