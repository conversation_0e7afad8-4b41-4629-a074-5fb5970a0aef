<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop" 
	xmlns:util="http://www.springframework.org/schema/util"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
	http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd 
	http://www.springframework.org/schema/tx    http://www.springframework.org/schema/tx/spring-tx.xsd
	http://www.springframework.org/schema/aop   http://www.springframework.org/schema/aop/spring-aop.xsd
	ttp://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="location" value="file:${config.path}/${context.path}/sysconfig.properties"/>
	</bean>
	
	<context:annotation-config />
    <context:component-scan base-package="com.ailk.newchnl">
    <!-- 
    	<context:include-filter type="regex" expression=".*.dao.*"/>
    	<context:include-filter type="regex" expression=".*.service.*"/>
    	 -->
    	<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>



	<!-- 
	<bean id="messageSource" class="org.springframework.context.support.ResourceBundleMessageSource">
		<property name="basename" value="resource.messages"/>
	</bean>
	-->
	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource"> 
		<!-- <property name="basename" value="classpath:resource/messages"/> -->
		<property name="cacheSeconds" value="-1" />
		<!-- <property name="parentMessageSource" ref="bizMessageSource"/> -->
		<property name="fallbackToSystemLocale" value="true"></property>		
		<property name="useCodeAsDefaultMessage" value="true"></property>
		<property name="basenames">
			<list>
				<value>classpath:messages</value>
			</list>
		</property> 
		<property name="defaultEncoding" value="UTF-8"/>
	</bean>
	
	<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="realm" ref="securityRealm"/>
	</bean>
 	<bean id="securityRealm" class="com.ailk.newchnl.web.security.SecurityRealm"/>
	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
	<bean id="anonymousFilter" class="org.apache.shiro.web.filter.authc.AnonymousFilter"/>
	<bean id="aaFilter" class="com.ailk.newchnl.web.security.SecurityFilter">
		<property name="usernameParam" value="username"/>
		<property name="passwordParam" value="password"/>
		<property name="captchaParam" value="verificationCode"/>
		<property name="unauthorizedUrl" value="/unauthorized"/>
	</bean>
	<bean id="logoutFilter" class="com.ailk.newchnl.web.security.LogoutFilter">
		<property name="redirectUrl" value="/login" />
	</bean>
	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">                 
		<property name="securityManager" ref="securityManager"/>
		<property name="loginUrl" value="/login"/>
		<!-- <property name="loginUrl" value="https://www.cas.com/login?service=http://www.example.com/shiro-cas" /> -->
		<property name="successUrl" value="/"/>
		<property name="unauthorizedUrl" value="/unauthorized"/>
		<property name="filters">
			<map>
				<entry key="anon" value-ref="anonymousFilter"/>
				<entry key="authc" value-ref="aaFilter"/>
				<entry key="logout" value-ref="logoutFilter"/>
				<!-- <entry key="casFilter" value-ref="casFilter"/> -->
			</map>
		</property>
		<property name="filterChainDefinitions"> 
			<value>
				<!-- /shiro-cas = casFilter -->
				/**/*.jpg = anon
				/**/*.png = anon
				/**/*.ico = anon
				/**/*.gif = anon
				/**/*.css = anon
				/**/*.js = anon
				/**/*.swf = anon
				/**/*.cab = anon
				/**/*.apk = anon
				/**/*.eot = anon
				/detect.html = anon
				/unauthorized = anon
				/verificationCode = anon
				/logout = logout
				/** = authc
			</value>
		</property>
	</bean>

	<!-- 
 	<bean id="casFilter" class="org.apache.shiro.cas.CasFilter">  
        <property name="failureUrl" value="/error.jsp"/>  
    </bean>  
    <bean id="casRealm" class="org.apache.shiro.cas.CasRealm">  
        <property name="defaultRoles" value="ROLE_USER"/>       
        <property name="casServerUrlPrefix" value="https://www.cas.com"/>  
        <property name="casService" value="http://www.example.com/shiro-cas"/>  
    </bean>  
    <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">      
        <property name="realm" ref="casRealm"/>  
        <property name="subjectFactory" ref="casSubjectFactory"/>  
    </bean>  
    <bean id="casSubjectFactory" class="org.apache.shiro.cas.CasSubjectFactory"/>  
    <bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>  
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">  
        <property name="staticMethod" value="org.apache.shiro.SecurityUtils.setSecurityManager"/>  
        <property name="arguments" ref="securityManager"/>  
    </bean>
 	 -->
 	<!-- 验证码图片 目前没用 -->
	<bean id="imageCaptchaService" class="com.google.code.kaptcha.impl.DefaultKaptcha">
        <property name="config">
            <bean class="com.google.code.kaptcha.util.Config">
                <constructor-arg>
                    <props>
                        <prop key="kaptcha.border">no</prop>
                        <prop key="kaptcha.border.color">black</prop>
                        <prop key="kaptcha.image.width">100</prop>
                        <prop key="kaptcha.image.height">30</prop>
                        <prop key="kaptcha.session.key">KAPTCHA_SESSION_KEY</prop>

                        <prop key="kaptcha.noise.color">66,139,202</prop>
						<prop key="kaptcha.noise.impl">com.google.code.kaptcha.impl.NoNoise</prop>
                        <!-- <prop key="kaptcha.noise.impl">com.google.code.kaptcha.impl.DefaultNoise</prop> -->
						
                        <!-- <prop key="kaptcha.obscurificator.impl">com.google.code.kaptcha.impl.WaterRipple</prop> -->
                        <prop key="kaptcha.obscurificator.impl">com.google.code.kaptcha.impl.ShadowGimpy</prop>
                        <!-- <prop key="kaptcha.GimpyEngine">WaterRipple</prop> -->
                        
                        <prop key="kaptcha.background.clear.from">238,238,238</prop>
                        <prop key="kaptcha.background.clear.to">238,238,238</prop>
                        <prop key="kaptcha.background.impl">com.google.code.kaptcha.impl.DefaultBackground</prop>
                        
                        <prop key="kaptcha.textproducer.char.string">acde234578gfynmnpwx</prop>
                        <prop key="kaptcha.textproducer.char.length">4</prop>
						<prop key="kaptcha.textproducer.impl">com.google.code.kaptcha.text.impl.DefaultTextCreator</prop>

                        <prop key="kaptcha.textproducer.font.names">Simsun, Arial, Courier</prop>
                        <prop key="kaptcha.textproducer.font.size">24</prop>
                        <prop key="kaptcha.textproducer.font.color">67,71,142</prop>
                        <prop key="kaptcha.textproducer.char.space">5</prop>
                        <prop key="kaptcha.word.impl">com.google.code.kaptcha.text.impl.DefaultWordRenderer</prop>
                    </props>
                </constructor-arg>
            </bean>
        </property>
    </bean>
    
	<!-- check code related using jcaptcha added by xuwz -->
	<!-- <bean id="imageCaptchaService" class="com.octo.captcha.service.image.DefaultManageableImageCaptchaService"/> -->
	<!-- <bean id="imageCaptchaService" class="com.octo.captcha.service.multitype.GenericManageableCaptchaService">
		<description>验证码服务</description>
		<constructor-arg index="0"><ref bean="imageCaptchaEngine"/></constructor-arg>
		<constructor-arg index="1"><value>300</value></constructor-arg>
		<constructor-arg index="2"><value>20000</value></constructor-arg>
		<constructor-arg index="3"><value>20000</value></constructor-arg>
	</bean>
	<bean id="imageCaptchaEngine" class="com.octo.captcha.engine.GenericCaptchaEngine">
		<description>图片引擎</description>
		<constructor-arg index="0"><list><ref bean="imageCaptchaFactory"/></list></constructor-arg>
	</bean>
	<bean id="imageCaptchaFactory" class="com.octo.captcha.image.gimpy.GimpyFactory" >
		<description>验证码工厂</description>
		<constructor-arg><ref bean="imageCaptchaWordGen"/></constructor-arg>
		<constructor-arg><ref bean="imageCaptchaWordToImage"/></constructor-arg>
	</bean>
	<bean id="imageCaptchaWordGen" class= "com.octo.captcha.component.word.wordgenerator.RandomWordGenerator" >
		<description>文字产生器，提供了好几种实现，经过比较选用了这种</description>
		<constructor-arg index="0"><value>0123456789</value></constructor-arg>
	</bean>
	<bean id="imageCaptchaWordToImage" class="com.octo.captcha.component.image.wordtoimage.ComposedWordToImage" >
		<description>图片生成器</description>
		<constructor-arg index="0"><ref bean="imageCaptchaFontGenRandom"/></constructor-arg>
		<constructor-arg index="1"><ref bean="imageCaptchaBackGenUni"/></constructor-arg>
		<constructor-arg index="2"><ref bean="imageCaptchaSimpleWhitePaster"/></constructor-arg>
	</bean>
	<bean id="imageCaptchaFontGenRandom" class="com.octo.captcha.component.image.fontgenerator.RandomFontGenerator" >
		<description>文字转换图片</description>
		<constructor-arg index="0" type="java.lang.Integer"><value>20</value></constructor-arg>
		<constructor-arg index="1" type="java.lang.Integer"><value>20</value></constructor-arg>
		<constructor-arg index="2" >
			<list>
				<ref bean="imageCaptchaFont1"/>
				<ref bean="imageCaptchaFont2"/>
				<ref bean="imageCaptchaFont3"/>
			</list>
		</constructor-arg>
	</bean> 
	<bean id="imageCaptchaBackGenUni" class="com.octo.captcha.component.image.backgroundgenerator.GradientBackgroundGenerator" >
		<constructor-arg index="0" type="java.lang.Integer"><value>72</value></constructor-arg>
		<constructor-arg index="1" type="java.lang.Integer"><value>32</value></constructor-arg>
		<constructor-arg index="2" type="java.awt.Color"><ref bean="imageCaptchaFirstColor"/></constructor-arg> 
		<constructor-arg index="3" type="java.awt.Color"><ref bean="imageCaptchaSecondColor"/></constructor-arg>
	</bean>
	<bean id="imageCaptchaSimpleWhitePaster" class="com.octo.captcha.component.image.textpaster.NonLinearTextPaster" >
		<constructor-arg index="0" type="java.lang.Integer"><value>4</value></constructor-arg>
		<constructor-arg index="1" type="java.lang.Integer"><value>4</value></constructor-arg>
		<constructor-arg index="2" type="java.awt.Color"><ref bean="imageCaptchaFontColor"/></constructor-arg>
	</bean>
	<bean id="imageCaptchaFirstColor" class="java.awt.Color" >
		<constructor-arg index="0" type="int" value="200"/>
		<constructor-arg index="1" type="int" value="255"/>
		<constructor-arg index="2" type="int" value="200"/>
	</bean>
	<bean id="imageCaptchaSecondColor" class="java.awt.Color" >
		<constructor-arg index="0" type="int" value="110"/>
		<constructor-arg index="1" type="int" value="120"/>
		<constructor-arg index="2" type="int" value="200"/>
	</bean>
	<bean id="imageCaptchaFontColor" class="java.awt.Color" >
		<constructor-arg index="0" type="int" value="60"/>
		<constructor-arg index="1" type="int" value="60"/>
		<constructor-arg index="2" type="int" value="60"/>
	</bean>
	<bean id="imageCaptchaFont1" class="java.awt.Font" >
		<constructor-arg index="0" type="String" value="Arial"/>
		<constructor-arg index="1" type="int" value="0"/>
		<constructor-arg index="2" type="int" value="10"/>
	</bean>
	<bean id="imageCaptchaFont2" class="java.awt.Font" >
		<constructor-arg index="0" type="String" value="Tahoma"/>
		<constructor-arg index="1" type="int" value="0"/>
		<constructor-arg index="2" type="int" value="10"/>
	</bean>
	<bean id="imageCaptchaFont3" class="java.awt.Font" >
		<constructor-arg index="0" type="String" value="Verdana"/>
		<constructor-arg index="1" type="int" value="0"/>
		<constructor-arg index="2" type="int" value="10"/>
	</bean> -->



	<!--hessian client 
	<bean class="example.SimpleObject">
		<property name="accountService" ref="accountService"/>
	</bean>
	<bean id="accountService" class="org.springframework.remoting.caucho.HessianProxyFactoryBean">
		<property name="serviceUrl" value="http://remotehost:8080/remoting/AccountService"/>
		<property name="serviceInterface" value="example.AccountService"/>
	</bean>
	
	<bean id="httpInvokerProxy" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
	    <property name="serviceUrl" value="http://remotehost:8080/remoting/AccountService"/>
	    <property name="serviceInterface" value="example.AccountService"/>
	    //following is using Commons HttpClient
		<property name="httpInvokerRequestExecutor">
		    <bean class="org.springframework.remoting.httpinvoker.CommonsHttpInvokerRequestExecutor"/>
		</property>
	</bean>
		
	 -->
	 
</beans>