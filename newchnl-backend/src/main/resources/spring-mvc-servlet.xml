<beans xmlns="http://www.springframework.org/schema/beans"   
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:mvc="http://www.springframework.org/schema/mvc"
    xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
	http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">
   

    <context:component-scan base-package="com.ailk.newchnl">
    <!-- 
    	<context:include-filter type="regex" expression=".*.controller.*"/> -->
    	<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Service"/> 
    </context:component-scan>
	
	<bean class ="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter" >  
    	<property name="messageConverters">  
	  		<list>
	  			<ref bean="mappingJacksonHttpMessageConverter" /> 
	  			<ref bean="stringHttpMessageConverter"/>
	  		</list>
		</property>  
						<!-- 对mvc提交date类型进行转换 -->
		<property name="webBindingInitializer"> 
			  <bean class="org.springframework.web.bind.support.ConfigurableWebBindingInitializer"> 
				 <property name="validator"> 
				  	<bean class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean" /> 
				  </property> 
				 <property name="propertyEditorRegistrars"> 
					 <array> 
					  	<bean class="com.ailk.newchnl.spring.CustomPropertyEditorRegistrar" /> 
					 </array> 
				 </property> 
			 </bean> 
		 </property>
	</bean>
	<bean id="stringHttpMessageConverter" class="org.springframework.http.converter.StringHttpMessageConverter">
		<property name="supportedMediaTypes"> 
			<list> 
				<value>text/plain;charset=UTF-8</value> 
			</list> 
		</property>	
	</bean>
	<bean id="mappingJacksonHttpMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter" />   

	<!-- 
	<bean class="org.springframework.web.servlet.resource.DefaultServletHttpRequestHandler"/>  
	<bean class="org.springframework.web.servlet.handler.SimpleUrlHandlerMapping"/>
	 -->
	<mvc:default-servlet-handler/>

	<mvc:interceptors>
		<bean id="localeChangeInterceptor" class="org.springframework.web.servlet.i18n.LocaleChangeInterceptor" />
		<mvc:interceptor>
			<mvc:mapping path="/config/**"/>
			<mvc:mapping path="/user/list"/>
			<bean class="com.ailk.newchnl.web.controller.CheckLoginInterceptor">
				<property name="contentType" value="application/json"/>
				<property name="encoding" value="UTF-8"/>
			</bean>
		</mvc:interceptor>
	</mvc:interceptors>

	<bean class="org.springframework.web.servlet.view.ContentNegotiatingViewResolver">
<!--		<property name="ignoreAcceptHeader" value="true" />-->
<!--		&lt;!&ndash; <property name="defaultContentType" value="application/json" /> &ndash;&gt;-->
<!--		<property name="defaultContentType" value="text/html" />-->
<!-- 		<property name="mediaTypes">-->
<!--			<map>-->
<!--				<entry key="html" value="text/html"/>-->
<!--				<entry key="json" value="application/json"/>-->
<!--				<entry key="xml" value="application/xml"/>-->
<!--			</map>-->
<!--		</property>-->
		<property name="viewResolvers">
			<list>
				<bean class="org.springframework.web.servlet.view.BeanNameViewResolver"/>
				<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
					<property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
					<property name="prefix" value="/WEB-INF/views/"/>
					<property name="suffix" value=".jsp"/>
				</bean>
			<!-- 
				<bean id="viewResolver" class="org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver">  
      				<property name="viewClass" value="org.springframework.web.servlet.view.freemarker.FreeMarkerView"/>
     				<property name="suffix" value=".fm"/>
					<property name="contentType" value="text/html; charset=UTF-8"/>
      			</bean>
				<bean class="org.springframework.web.servlet.view.velocity.VelocityViewResolver">  
					<property name="cache" value="false"/>
					<property name="prefix" value="">
					<property name="suffix" value=".html">
					<property name="contentType" value="text/html;charset=UTF-8"/>  
            	</bean>
      		 -->
      		 
			</list>
		</property>
		<property name="defaultViews">
			<list>
				<bean class="org.springframework.web.servlet.view.json.MappingJackson2JsonView" />

				<bean class="org.springframework.web.servlet.view.xml.MarshallingView">  
					<property name="marshaller">
						<bean class="org.springframework.oxm.xstream.XStreamMarshaller"/>  
	                </property>
				</bean>
			</list>
		</property>
	</bean>

	<!-- 
	<bean id="multipartResolver" class="org.springframework.web.multipart.support.StandardServletMultipartResolver">  
	</bean>
	 --> 
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">  
		<property name="defaultEncoding" value="UTF-8"/>  
	</bean>	 
	<!-- 
	<mvc:view-controller path="/" view-name="home"/>
	 -->
	
	<!-- 
	<bean class="org.springframework.web.servlet.view.velocity.VelocityConfigurer">
		<property name="resourceLoaderPath" value="/WEB-INF/velocity/"></property>
	</bean>	 
	 -->
	 
	<!-- 
	<bean class="org.springframework.web.servlet.mvc.annotation.DefaultAnnotationHandlerMapping"/>
	<bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter"/>  
	from 3.1,
	DefaultAnnotationHandlerMapping -> RequestMappingHandlerMapping,
	AnnotationMethodHandlerAdapter -> RequestMappingHandlerAdapter,
	AnnotationMethodHandlerExceptionResolver -> ExceptionHandlerExceptionResolver
	 -->
	<bean id="requestMappingHandlerMapping" class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping"/>
	<bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter"/> 
	
	<mvc:annotation-driven conversion-service="conversionService" />
	<bean id="conversionService" class="org.springframework.format.support.FormattingConversionServiceFactoryBean">
		<property name="converters">
			<list/>
		</property>	
	</bean>	
	
	<bean id="webExceptionResolver" class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">    
        <property name="defaultErrorView" value="/error" />
        <property name="exceptionAttribute" value="exception" />
        <property name="exceptionMappings">    
            <props>    
                <prop key="com.a.a.a.exception.BaseServiceException">
                    redirect:/error.jsp?flag=BaseServiceException    
                </prop>
            </props>
        </property>    
	</bean>
	
	<bean id="localeResolver" class="org.springframework.web.servlet.i18n.CookieLocaleResolver">
		<property name="defaultLocale" value="zh_CN"/>
	</bean>

	 <bean class="com.ailk.newchnl.util.SpringContextUtil" scope="singleton"/>

</beans>