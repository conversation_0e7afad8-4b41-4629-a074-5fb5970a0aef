traceEnabled: true
logEnabled: true
metricEnabled: true
# must be a power of 2
localBufferSize: 8192
messageBatchSize: 1
debugLevel: INFO
selfMonitorEnabled: false
ipConfigPrefix: server.ip
timeSyncEnabled: false
ntpServer: *************
sysCode: SHNGQD2
sqlParamCollectEnabled: true
# file|kafka
producerType: kafka
producerApi: Java
message:
  default:
    topic: LOG4X-MSG-TOPIC
    # file|discard
    bufferFullPolicy: discard
  trace:
    topic: LOG4X-TRACE-TOPIC
    probeType: SRV
    sampleRatio: 1
    isEntrance: false
    # file|discard
    bufferFullPolicy: discard
  log:
    topic: LOG4X-LOG-TOPIC
  custom:
    - name: 4a
      topic: CLPO-4A-TOPIC
  metric:
    topic: LOG4X-METRIC-TOPIC
    timeInterval: 60
    thread:
      enabled: true
      category:
        - name: "CSF"
          pattern: "CsfServer-RequestHandleThread"
        - name: "Spark"
          pattern: "Spark_"
    dataSource:
      enabled: true
      type: Appframe
# kafka settings
kafka:
  default:
    ################# Java API ################ ************:9092,************:9092,************:9092
    bootstrap.servers: "${kafka.host}"
    key.serializer: org.apache.kafka.common.serialization.ByteArraySerializer
    value.serializer: org.apache.kafka.common.serialization.ByteArraySerializer
    compression.type: snappy
    # 32MB
    buffer.memory: 33554432
    # for each partition
    batch.size: 8192
    # wait up to 100ms before sending
    linger.ms: 100
    # greater than 0 may cause record duplication
    retries: 0
    # 0|1|all
    acks: 1
# file settings
file:
  dataDir: "${user.home}/log4x/data"
  bufferedIO: true
  bufferSize: 8192
  fileNamePattern: "%d{yyyy-MM-dd}.zip"
  maxHistory: 7
# if matched, drop the messages
filters:
  - "^.*CheckSVImpl.heartbeat"
  - "^.*CommonSV.*getDBSysDate"
  - "^.*DefaultAction.getSysDateTime"
  - "^.*InstanceQrySV.*getDoneCode"
  - "^.*InstanceQrySV.*getTimeSysdate"
  - "^.*SecLoginSV.*setUserInfo"
  - "^.*OrderCustSV.*setOrderCode"
  - "^.*OrderCustSV.*clearOrderCode"
  - "select 1 from dual"
